import { Tool } from "../..";
import { BUILT_IN_GROUP_NAME, BuiltInToolNames } from "../builtIn";

export interface EditToolArgs {
  filepath: string;
  changes: string;
}

export const editFileTool: Tool = {
  type: "function",
  displayTitle: "Edit File",
  wouldLikeTo: "edit {{{ filepath }}}",
  isCurrently: "editing {{{ filepath }}}",
  hasAlready: "edited {{{ filepath }}}",
  group: BUILT_IN_GROUP_NAME,
  readonly: false,
  function: {
    name: BuiltInToolNames.EditExistingFile,
    description:
      "Use this tool to edit an existing file. If you don't know the contents of the file, read it first.When the tool is successfully called, it will return the latest complete content of the modified file.",
    parameters: {
      type: "object",
      required: ["filepath", "changes", "startLine", "endLine"],
      properties: {
        filepath: {
          type: "string",
          description:
            "The path of the file to edit, relative to the root of the workspace.",
        },
        changes: {
          type: "string",
          description:
            "Any modifications to the file, showing only needed changes. Do NOT wrap this in a codeblock or write anything besides the code changes. In larger files, use brief language-appropriate placeholders for large unmodified sections, e.g. '// ... existing code ...'",
        },
        startLine: {
          type: "number",
          description: "当前修改内容相关的代码块的开始行号",
        },
        endLine: {
          type: "number",
          description: "当修改内容相关的代码块的结束行号",
        },
      },
    },
  },
};
