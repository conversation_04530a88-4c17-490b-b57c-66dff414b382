import java.net.InetAddress;
import java.net.UnknownHostException;

public class HelloWorld {

    public static void main(String[] args) {
        System.out.println("Hello, World!");
    }

    public static String getCurrentTime(String time) {
        if (time.equals("")){
            return "No time provided";
        }
        // 获取当前时间，以毫秒为单位
        String currentTime = String.valueOf(System.currentTimeMillis());

        // 打印当前时间
        System.out.println("Current time: " + currentTime);

        // 打印 "Hello, World!" 五次
        for (int i = 0; i < 5; i++) {
            System.out.println("Hello, World!");
        }

        return currentTime;
    }

    // 封装加法操作
    public static int add(int a, int b) {
        return a + b;
    }
    // 封装减法
    public static int subtract(int a, int b) {
        return a - b;
    }
    // 封装乘法
    public static int multiply(int a, int b) {
        return a * b;
    }
    // 封装除法
    public static int divide(int a, int b) {
        if (b == 0) {
            throw new ArithmeticException("除数不能为0");
        }
        return a / b;
    }

    public static String getLocalIpAddress() {
        try {
            InetAddress localHost = InetAddress.getLocalHost();
            return localHost.getHostAddress();
        } catch (UnknownHostException e) {
            e.printStackTrace();
            return null;
        }
    }
}
