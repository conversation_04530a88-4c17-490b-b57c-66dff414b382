{"name": "my-react-project", "version": "1.0.0", "description": "A React project using Radix UI for accessible components", "main": "index.js", "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "keywords": ["react", "radix-ui", "accessibility", "frontend"], "author": "Your Name", "license": "MIT", "dependencies": {"@radix-ui/react-accordion": "^1.0.0", "@radix-ui/react-alert-dialog": "^1.0.0", "@radix-ui/react-checkbox": "^1.0.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-scripts": "^5.0.0"}, "devDependencies": {"@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "typescript": "^4.5.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}