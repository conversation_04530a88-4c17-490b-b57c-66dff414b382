import { resolveRelativePathInDir } from "core/util/ideUtils";
import { ClientToolImpl } from "./callClientTool";

export const editToolImpl: ClientToolImpl = async (
  args,
  toolCallId,
  extras,
) => {
  if (!extras.streamId) {
    throw new Error("Invalid apply state");
  }
  const firstUriMatch = await resolveRelativePathInDir(
    args.filepath,
    extras.ideMessenger.ide,
  );
  if (!firstUriMatch) {
    throw new Error(`${args.filepath} does not exist`);
  }
  const apply = await extras.ideMessenger.request("applyToFile", {
    streamId: extras.streamId,
    text: args.changes,
    startLine: args.startLine,
    endLine: args.endLine,
    toolCallId,
    filepath: firstUriMatch,
  });
  if (apply.status === "error") {
    throw new Error(apply.error);
  }
  const state = extras.getState();
  const autoAccept = !!state.config.config.ui?.autoAcceptEditToolDiffs;
  if (autoAccept) {
    const out = await extras.ideMessenger.request("acceptDiff", {
      streamId: extras.streamId,
      filepath: firstUriMatch,
    });
    if (out.status === "error") {
      throw new Error(out.error);
    }

    // Return the updated file content to the model
    const fileContent = out.fileContent || "File content not available";
    return {
      respondImmediately: true,
      output: `File ${args.filepath} has been successfully edited. Current file content:\n\n${fileContent}`,
    };
  }
  return {
    respondImmediately: false,
    output: undefined, // No immediate output.
  };
};
