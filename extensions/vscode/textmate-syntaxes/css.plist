<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>fileTypes</key>
	<array>
		<string>css</string>
		<string>css.erb</string>
	</array>
	<key>keyEquivalent</key>
	<string>^~C</string>
	<key>name</key>
	<string>CSS</string>
	<key>patterns</key>
	<array>
		<dict>
			<key>include</key>
			<string>#comment-block</string>
		</dict>
		<dict>
			<key>include</key>
			<string>#selector</string>
		</dict>
		<dict>
			<key>begin</key>
			<string>\s*((@)charset\b)\s*</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>keyword.control.at-rule.charset.css</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.keyword.css</string>
				</dict>
			</dict>
			<key>end</key>
			<string>\s*((?=;|$))</string>
			<key>name</key>
			<string>meta.at-rule.charset.css</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#string-double</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#string-single</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>\s*((@)import\b)\s*</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>keyword.control.at-rule.import.css</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.keyword.css</string>
				</dict>
			</dict>
			<key>end</key>
			<string>\s*((?=;|\}))</string>
			<key>name</key>
			<string>meta.at-rule.import.css</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#string-double</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#string-single</string>
				</dict>
				<dict>
					<key>begin</key>
					<string>\s*(url)\s*(\()\s*</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>support.function.url.css</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.function.css</string>
						</dict>
					</dict>
					<key>end</key>
					<string>\s*(\))\s*</string>
					<key>endCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.function.css</string>
						</dict>
					</dict>
					<key>patterns</key>
					<array>
						<dict>
							<key>match</key>
							<string>[^'") \t]+</string>
							<key>name</key>
							<string>variable.parameter.url.css</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#string-single</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#string-double</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>include</key>
					<string>#media-query-list</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>^\s*((@)font-face)\s*(?=\{)</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>keyword.control.at-rule.font-face.css</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.keyword.css</string>
				</dict>
			</dict>
			<key>end</key>
			<string>(?!\G)</string>
			<key>name</key>
			<string>meta.at-rule.font-face.css</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#rule-list</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(?=^\s*@media\s*.*?\{)</string>
			<key>end</key>
			<string>\s*(\})</string>
			<key>endCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.section.property-list.end.css</string>
				</dict>
			</dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>^\s*((@)media)(?=.*?\{)</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.control.at-rule.media.css</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.keyword.css</string>
						</dict>
						<key>3</key>
						<dict>
							<key>name</key>
							<string>support.constant.media.css</string>
						</dict>
					</dict>
					<key>end</key>
					<string>\s*(?=\{)</string>
					<key>name</key>
					<string>meta.at-rule.media.css</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#media-query-list</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>\s*(\{)</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.property-list.begin.css</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(?=\})</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>$self</string>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>^\s*((@)(-(o|ms)-)?viewport)\s*(?=\{)</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>keyword.control.at-rule.viewport.css</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.keyword.css</string>
				</dict>
			</dict>
			<key>end</key>
			<string>(?!\G)</string>
			<key>name</key>
			<string>meta.at-rule.viewport.css</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#rule-list</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(?=\{)</string>
			<key>end</key>
			<string>(?!\G)</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#rule-list</string>
				</dict>
			</array>
		</dict>
	</array>
	<key>repository</key>
	<dict>
		<key>color-values</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>comment</key>
					<string>Basic color keywords: http://www.w3.org/TR/css3-color/#html4</string>
					<key>match</key>
					<string>\b(aqua|black|blue|fuchsia|gray|green|lime|maroon|navy|olive|orange|purple|red|silver|teal|white|yellow)\b</string>
					<key>name</key>
					<string>support.constant.color.w3c-standard-color-name.css</string>
				</dict>
				<dict>
					<key>comment</key>
					<string>Extended color keywords: http://www.w3.org/TR/css3-color/#svg-color</string>
					<key>match</key>
					<string>\b(aliceblue|antiquewhite|aquamarine|azure|beige|bisque|blanchedalmond|blueviolet|brown|burlywood|cadetblue|chartreuse|chocolate|coral|cornflowerblue|cornsilk|crimson|cyan|darkblue|darkcyan|darkgoldenrod|darkgray|darkgreen|darkgrey|darkkhaki|darkmagenta|darkolivegreen|darkorange|darkorchid|darkred|darksalmon|darkseagreen|darkslateblue|darkslategray|darkslategrey|darkturquoise|darkviolet|deeppink|deepskyblue|dimgray|dimgrey|dodgerblue|firebrick|floralwhite|forestgreen|gainsboro|ghostwhite|gold|goldenrod|greenyellow|grey|honeydew|hotpink|indianred|indigo|ivory|khaki|lavender|lavenderblush|lawngreen|lemonchiffon|lightblue|lightcoral|lightcyan|lightgoldenrodyellow|lightgray|lightgreen|lightgrey|lightpink|lightsalmon|lightseagreen|lightskyblue|lightslategray|lightslategrey|lightsteelblue|lightyellow|limegreen|linen|magenta|mediumaquamarine|mediumblue|mediumorchid|mediumpurple|mediumseagreen|mediumslateblue|mediumspringgreen|mediumturquoise|mediumvioletred|midnightblue|mintcream|mistyrose|moccasin|navajowhite|oldlace|olivedrab|orangered|orchid|palegoldenrod|palegreen|paleturquoise|palevioletred|papayawhip|peachpuff|peru|pink|plum|powderblue|rebeccapurple|rosybrown|royalblue|saddlebrown|salmon|sandybrown|seagreen|seashell|sienna|skyblue|slateblue|slategray|slategrey|snow|springgreen|steelblue|tan|thistle|tomato|turquoise|violet|wheat|whitesmoke|yellowgreen)\b</string>
					<key>name</key>
					<string>support.constant.color.w3c-extended-color-name.css</string>
				</dict>
				<dict>
					<key>begin</key>
					<string>(hsla?|rgba?)\s*(\()</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>support.function.misc.css</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.function.css</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(\))</string>
					<key>endCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.function.css</string>
						</dict>
					</dict>
					<key>patterns</key>
					<array>
						<dict>
							<key>match</key>
							<string>(?x)\b
							    (0*((1?[0-9]{1,2})|(2([0-4][0-9]|5[0-5])))\s*,\s*){2}
							    (0*((1?[0-9]{1,2})|(2([0-4][0-9]|5[0-5])))\b)
							    (\s*,\s*((0?\.[0-9]+)|[0-1]))?
							</string>
							<key>name</key>
							<string>constant.other.color.rgb-value.css</string>
						</dict>
						<dict>
							<key>match</key>
							<string>\b([0-9]{1,2}|100)\s*%,\s*([0-9]{1,2}|100)\s*%,\s*([0-9]{1,2}|100)\s*%</string>
							<key>name</key>
							<string>constant.other.color.rgb-percentage.css</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#numeric-values</string>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<key>comment-block</key>
		<dict>
			<key>begin</key>
			<string>/\*</string>
			<key>captures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.comment.css</string>
				</dict>
			</dict>
			<key>end</key>
			<string>\*/</string>
			<key>name</key>
			<string>comment.block.css</string>
		</dict>
		<key>media-query</key>
		<dict>
			<key>begin</key>
			<string>(?i)\s*(only|not)?\s*(all|aural|braille|embossed|handheld|print|projection|screen|speech|tty|tv)?</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>keyword.operator.logic.media.css</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>support.constant.media.css</string>
				</dict>
			</dict>
			<key>end</key>
			<string>\s*(?:(,)|(?=[{;]))</string>
			<key>endCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.arbitrary-repitition.css</string>
				</dict>
			</dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>\s*(and)?\s*(\()\s*</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.operator.logic.media.css</string>
						</dict>
					</dict>
					<key>end</key>
					<string>\)</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>begin</key>
							<string>(?x)
	                            (
	                                ((min|max)-)?
	                                (
	                                    ((device-)?(height|width|aspect-ratio))|
	                                    (color(-index)?)|monochrome|resolution
	                                )
	                            )|grid|scan|orientation|((any-)?(pointer|hover))
	                            \s*(?=[:)])</string>
							<key>beginCaptures</key>
							<dict>
								<key>0</key>
								<dict>
									<key>name</key>
									<string>support.type.property-name.media.css</string>
								</dict>
							</dict>
							<key>end</key>
							<string>(:)|(?=\))</string>
							<key>endCaptures</key>
							<dict>
								<key>1</key>
								<dict>
									<key>name</key>
									<string>punctuation.separator.key-value.css</string>
								</dict>
							</dict>
						</dict>
						<dict>
							<key>match</key>
							<string>\b(portrait|landscape|progressive|interlace|none|coarse|fine|on-demand|hover)</string>
							<key>name</key>
							<string>support.constant.property-value.css</string>
						</dict>
						<dict>
							<key>captures</key>
							<dict>
								<key>1</key>
								<dict>
									<key>name</key>
									<string>constant.numeric.css</string>
								</dict>
								<key>2</key>
								<dict>
									<key>name</key>
									<string>keyword.operator.arithmetic.css</string>
								</dict>
								<key>3</key>
								<dict>
									<key>name</key>
									<string>constant.numeric.css</string>
								</dict>
							</dict>
							<key>match</key>
							<string>\s*(\d+)(/)(\d+)</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#numeric-values</string>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<key>media-query-list</key>
		<dict>
			<key>begin</key>
			<string>\s*(?=[^{;])</string>
			<key>end</key>
			<string>\s*(?=[{;])</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#media-query</string>
				</dict>
			</array>
		</dict>
		<key>numeric-values</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.constant.css</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(#)([0-9a-fA-F]{3}|[0-9a-fA-F]{6})\b</string>
					<key>name</key>
					<string>constant.other.color.rgb-value.css</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.other.unit.css</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(?x)
					    (?:-|\+)?(?:(?:[0-9]+(?:\.[0-9]+)?)|(?:\.[0-9]+))
					    ((?:px|pt|ch|cm|mm|in|r?em|ex|pc|vw|vh|vmin|vmax|deg|g?rad|turn|dpi|dpcm|dppx|fr|s|ms|Hz|kHz)\b|%)?
					</string>
					<key>name</key>
					<string>constant.numeric.css</string>
				</dict>
			</array>
		</dict>
		<key>property-values</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>\b(absolute|add|additive|all(-scroll)?|allow-end|alpha|alphabetic|alternate(-reverse)?|always|any|armenian|auto|avoid(-(column|flex|line|page|region))?|backwards|balance(-all)?|bar|baseline|below|bengali|bevel|bidi-override|block(-(end|start))?|bold|bolder|border-box|both|bottom|box-decoration|break-(all|word)|bullets|cambodian|capitalize|center|central|char|circle|cjk-(decimal|earthly-branch|heavenly-stem|ideographic)|clear|clip|clone|coarse|col-resize|collapse|color(-(burn|dodge))?|column(-reverse)?|contain|content(-box)?|contents|cover|create|crisp-edges|currentcolor|cyclic|darken|densedashed|decimal-leading-zero|decimal|default|dense|devanagari|difference|digits|disabled|disc|disclosure-(closed|open)|display|distribute-all-lines|distribute(-(letter|space))?|dot|dotted|double(-circle)?|e-resize|each-line|ease(-(in(-out)?|out))?|economy|edges|ellipsis|embed|end|ethiopic-numeric|evenodd|exact|exclude|exclusion|extends|fade|fill(-(available|box))?|filled|first(-baseline)?|fit-content|fixed|flex(-(end|start))?|flow(-root)?|force-end|forwards|fragments|from-image|full-width|geometricPrecision|georgian|grid|groove|gujarati|gurmukhi|hand|hanging|hard-light|hebrew|help|hidden|hiragana(-iroha)?|horizontal(-tb)?|hue|ideograph-(alpha|numeric|parenthesis|space)|ideographic|inactive|infinite|inherit|initial|ink|inline(-(block|end|flex|grid|list-item|start|table))?|inset|inside|inter-(character|ideograph|word)|intersect|invalid|invert|isolate(-override)?|italic|japanese-(formal|informal)|justify(-all)?|katakana(-iroha)?|keep-all|khmer|korean-(hangul-formal|hanja-(formal|informal))|landscape|lao|last(-baseline)?|leading-spaces|left|legacy|lighter|line(-(edge|through))?|list-(container|item)|local|logical|loose|lower-(alpha|armenian|greek|latin|roman)|lowercase|lr-tb|ltr|luminance|luminosity|malayalam|mandatory|manipulation|manual|margin-box|marker|match-parent|mathematical|max-content|maximum|medium|middle|minimum|mixed|mongolian|move|multiply|myanmar|n-resize|ne-resize|newspaper|no-(clip|close-quote|composite|compress|drop|open-quote|repeat)|non-blocking|none|nonzero|normal|notch|not-allowed|nowrap|numbers|numeric|nw-resize|objects|oblique|open(-quote)?|oriya|optimize(Legibility|Quality|Speed)|outset|outside(-shape)?|overlay|overline|padding-box|page|paginate|paint|pan-(x|y)|paused|persian|physical|pixelated|plaintext|pointer|portrait|pre(-(line|wrap(-auto)?))?|preserve(-(auto|breaks|spaces|trim))?|progress|proximity|punctuation|region|relative|repeat(-(x|y))?|reverse|revert|ridge|right|rotate|row(-reverse)?|row-resize|rtl|ruby(-((base|text)(-container)?))?|run-in|running|s-resize|saturation|scale-down|scroll(-position)?|se-resize|self-(end|start)|separate|sesame|show|sideways(-(left|lr|right|rl))?|simp-chinese-(formal|informal)|slice|small-caps|smooth|snap(-(block|inline))?|soft-light|solid|space(-(adjacent|around|between|end|evenly|start))?|spaces|spell-out|spread|square|start|static|step-(end|start)|sticky|stretch|strict|stroke-box|sub|subgrid|subtract|super|sw-resize|symbolic|table(-(caption|cell|(column|row)(-group)?|footer-group|header-group))?|tamil|tb-rl|telugu|text(-(bottom|top))?|thai|thick|thin|tibetan|top|transparent|triangle|trim-(adjacent|end|inner|start)|true|under|underline|underscore|unsafe|unset|upper-(alpha|latin|roman)|uppercase|upright|use-glyph-orientation|vertical(-(ideographic|lr|rl|text))?|view-box|visible(Painted|Fill|Stroke)?|w-resize|wait|whitespace|words|wrap|zero|smaller|larger|((xx?-)?(small|large))|painted|fill|stroke)\b</string>
					<key>name</key>
					<string>support.constant.property-value.css</string>
				</dict>
				<dict>
					<key>match</key>
					<string>(\b(?i:arial|century|comic|courier|garamond|georgia|helvetica|impact|lucida|symbol|system|tahoma|times|trebuchet|utopia|verdana|webdings|sans-serif|serif|monospace)\b)</string>
					<key>name</key>
					<string>support.constant.font-name.css</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#numeric-values</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#color-values</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#string-double</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#string-single</string>
				</dict>
				<dict>
					<key>begin</key>
					<string>(blur|drop-shadow|perspective|rect|translate(3d|X|Y|Z)?)\s*(\()</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>support.function.misc.css</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.function.css</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(\))</string>
					<key>endCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.function.css</string>
						</dict>
					</dict>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#numeric-values</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>(cubic-bezier|matrix(3d)?|scale(3d|X|Y|Z)?)\s*(\()</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>support.function.misc.css</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.function.css</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(\))</string>
					<key>endCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.function.css</string>
						</dict>
					</dict>
					<key>patterns</key>
					<array>
						<dict>
							<key>match</key>
							<string>(?:-|\+)?(?:(?:[0-9]+(?:\.[0-9]+)?)|(?:\.[0-9]+))</string>
							<key>name</key>
							<string>constant.numeric.css</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>((hue-)?rotate|skew(X|Y|Z)?)\s*(\()</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>support.function.misc.css</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.function.css</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(\))</string>
					<key>endCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.function.css</string>
						</dict>
					</dict>
					<key>patterns</key>
					<array>
						<dict>
							<key>match</key>
							<string>(?x)
					    		(?:-|\+)?(?:(?:[0-9]+(?:\.[0-9]+)?)|(?:\.[0-9]+))
								((?:deg|g?rad|turn)\b)
							</string>
							<key>name</key>
							<string>constant.numeric.css</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>(brightness|contrast|grayscale|invert|opacity|saturate|sepia)\s*(\()</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>support.function.misc.css</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.function.css</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(\))</string>
					<key>endCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.function.css</string>
						</dict>
					</dict>
					<key>patterns</key>
					<array>
						<dict>
							<key>match</key>
							<string>(?x)
					    		(?:-|\+)?(?:(?:[0-9]+(?:\.[0-9]+)?)|(?:\.[0-9]+))
								(%)?
							</string>
							<key>name</key>
							<string>constant.numeric.css</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>(format|local|url|attr|counter|counters)\s*(\()</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>support.function.misc.css</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.function.css</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(\))</string>
					<key>endCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.function.css</string>
						</dict>
					</dict>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#string-single</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#string-double</string>
						</dict>
						<dict>
							<key>match</key>
							<string>[^'") \t]+</string>
							<key>name</key>
							<string>variable.parameter.misc.css</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>(var)\s*(\()</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>support.function.misc.css</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.function.css</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(\))</string>
					<key>endCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.function.css</string>
						</dict>
					</dict>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#variable-name</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>match</key>
					<string>\!\s*important</string>
					<key>name</key>
					<string>keyword.other.important.css</string>
				</dict>
			</array>
		</dict>
		<key>rule-list</key>
		<dict>
			<key>begin</key>
			<string>\{</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.section.property-list.begin.css</string>
				</dict>
			</dict>
			<key>end</key>
			<string>\}</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.section.property-list.end.css</string>
				</dict>
			</dict>
			<key>name</key>
			<string>meta.property-list.css</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#comment-block</string>
				</dict>
				<dict>
					<key>begin</key>
					<string>(?&lt;![-a-z])(?=[-a-z])</string>
					<key>end</key>
					<string>$|(?![-a-z])</string>
					<key>name</key>
					<string>meta.property-name.css</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#variable-name</string>
						</dict>
						<dict>
							<key>match</key>
							<string>(-(webkit|moz|o|ms|khtml)-)?(zoom|z-index|y|x|writing-mode|wrap|wrap-through|wrap-inside|wrap-flow|wrap-before|wrap-after|word-wrap|word-spacing|word-break|word|will-change|width|widows|white-space-collapse|white-space|white|weight|volume|voice-volume|voice-stress|voice-rate|voice-pitch-range|voice-pitch|voice-family|voice-duration|voice-balance|voice|visibility|vertical-align|vector-effect|variant|user-zoom|user-select|up|unicode-(bidi|range)|trim|translate|transition-timing-function|transition-property|transition-duration|transition-delay|transition|transform-style|transform-origin|transform-box|transform|touch-action|top-width|top-style|top-right-radius|top-left-radius|top-color|top|timing-function|text-wrap|text-underline-position|text-transform|text-spacing|text-space-trim|text-space-collapse|text-size-adjust|text-shadow|text-replace|text-rendering|text-overflow|text-outline|text-orientation|text-justify|text-indent|text-height|text-emphasis-style|text-emphasis-skip|text-emphasis-position|text-emphasis-color|text-emphasis|text-decoration-style|text-decoration-stroke|text-decoration-skip|text-decoration-line|text-decoration-fill|text-decoration-color|text-decoration|text-combine-upright|text-anchor|text-align-last|text-align-all|text-align|text|target-position|target-new|target-name|target|table-layout|tab-size|system|symbols|suffix|style-type|style-position|style-image|style|stroke-width|stroke-opacity|stroke-miterlimit|stroke-linejoin|stroke-linecap|stroke-dashoffset|stroke-dasharray|stroke|string-set|stretch|stress|stop-opacity|stop-color|stacking-strategy|stacking-shift|stacking-ruby|stacking|src|speed|speech-rate|speech|speak-punctuation|speak-numeral|speak-header|speak-as|speak|span|spacing|space-collapse|space|solid-opacity|solid-color|sizing|size-adjust|size|shape-rendering|shape-padding|shape-outside|shape-margin|shape-inside|shape-image-threshold|shadow|scroll-snap-type|scroll-snap-points-y|scroll-snap-points-x|scroll-snap-destination|scroll-snap-coordinate|scroll-behavior|scale|ry|rx|respond-to|rule-width|rule-style|rule-color|rule|ruby-span|ruby-position|ruby-overhang|ruby-merge|ruby-align|ruby|rows|rotation-point|rotation|rotate|role|right-width|right-style|right-color|right|richness|rest-before|rest-after|rest|resource|resolution|resize|reset|replace|repeat|rendering-intent|region-fragment|rate|range|radius|r|quotes|punctuation-trim|punctuation|property|profile|presentation-level|presentation|prefix|position|pointer-events|point|play-state|play-during|play-count|pitch-range|pitch|phonemes|perspective-origin|perspective|pause-before|pause-after|pause|page-policy|page-break-inside|page-break-before|page-break-after|page|padding-top|padding-right|padding-left|padding-inline-start|padding-inline-end|padding-bottom|padding-block-start|padding-block-end|padding|pad|pack|overhang|overflow-y|overflow-x|overflow-wrap|overflow-style|overflow-inline|overflow-block|overflow|outline-width|outline-style|outline-offset|outline-color|outline|orphans|origin|orientation|orient|ordinal-group|order|opacity|offset-start|offset-inline-start|offset-inline-end|offset-end|offset-block-start|offset-block-end|offset-before|offset-after|offset|object-position|object-fit|numeral|new|negative|nav-up|nav-right|nav-left|nav-index|nav-down|nav|name|move-to|motion-rotation|motion-path|motion-offset|motion|model|mix-blend-mode|min-zoom|min-width|min-inline-size|min-height|min-block-size|min|max-zoom|max-width|max-lines|max-inline-size|max-height|max-block-size|max|mask-type|mask-size|mask-repeat|mask-position|mask-origin|mask-mode|mask-image|mask-composite|mask-clip|mask-border-width|mask-border-source|mask-border-slice|mask-border-repeat|mask-border-outset|mask-border-mode|mask-border|mask|marquee-style|marquee-speed|marquee-play-count|marquee-loop|marquee-direction|marquee|marks|marker-start|marker-side|marker-mid|marker-end|marker|margin-top|margin-right|margin-left|margin-inline-start|margin-inline-end|margin-bottom|margin-block-start|margin-block-end|margin|list-style-type|list-style-position|list-style-image|list-style|list|lines|line-stacking-strategy|line-stacking-shift|line-stacking-ruby|line-stacking|line-snap|line-height|line-grid|line-break|line|lighting-color|level|letter-spacing|length|left-width|left-style|left-color|left|label|kerning|justify-self|justify-items|justify-content|justify|iteration-count|isolation|inline-size|inline-box-align|initial-value|initial-size|initial-letter-wrap|initial-letter-align|initial-letter|initial-before-align|initial-before-adjust|initial-after-align|initial-after-adjust|index|indent|increment|image-rendering|image-resolution|image-orientation|image|icon|hyphens|hyphenate-limit-zone|hyphenate-limit-lines|hyphenate-limit-last|hyphenate-limit-chars|hyphenate-character|hyphenate|height|header|hanging-punctuation|grid-template-rows|grid-template-columns|grid-template-areas|grid-template|grid-row-start|grid-row-gap|grid-row-end|grid-row|grid-rows|grid-gap|grid-column-start|grid-column-gap|grid-column-end|grid-column|grid-columns|grid-auto-rows|grid-auto-flow|grid-auto-columns|grid-area|grid|glyph-orientation-vertical|glyph-orientation-horizontal|gap|font-weight|font-variant-position|font-variant-numeric|font-variant-ligatures|font-variant-east-asian|font-variant-caps|font-variant-alternates|font-variant|font-synthesis|font-style|font-stretch|font-size-adjust|font-size|font-language-override|font-kerning|font-feature-settings|font-family|font|flow-into|flow-from|flow|flood-opacity|flood-color|float-offset|float|flex-wrap|flex-shrink|flex-grow|flex-group|flex-flow|flex-direction|flex-basis|flex|fit-position|fit|filter|fill-rule|fill-opacity|fill|family|fallback|enable-background|empty-cells|emphasis|elevation|duration|drop-initial-value|drop-initial-size|drop-initial-before-align|drop-initial-before-adjust|drop-initial-after-align|drop-initial-after-adjust|drop|down|dominant-baseline|display-role|display-model|display|direction|delay|decoration-break|decoration|cy|cx|cursor|cue-before|cue-after|cue|crop|counter-set|counter-reset|counter-increment|counter|count|corner-shape|corners|continue|content|contain|columns|column-width|column-span|column-rule-width|column-rule-style|column-rule-color|column-rule|column-gap|column-fill|column-count|column-break-before|column-break-after|column|color-rendering|color-profile|color-interpolation-filters|color-interpolation|color-adjust|color|collapse|clip-rule|clip-path|clip|clear|character|caret-shape|caret-color|caret|caption-side|buffered-rendering|break-inside|break-before|break-after|break|box-suppress|box-snap|box-sizing|box-shadow|box-pack|box-orient|box-ordinal-group|box-lines|box-flex-group|box-flex|box-direction|box-decoration-break|box-align|box|bottom-width|bottom-style|bottom-right-radius|bottom-left-radius|bottom-color|bottom|border-width|border-top-width|border-top-style|border-top-right-radius|border-top-left-radius|border-top-color|border-top|border-style|border-spacing|border-right-width|border-right-style|border-right-color|border-right|border-radius|border-limit|border-length|border-left-width|border-left-style|border-left-color|border-left|border-inline-start-width|border-inline-start-style|border-inline-start-color|border-inline-start|border-inline-end-width|border-inline-end-style|border-inline-end-color|border-inline-end|border-image-width|border-image-transform|border-image-source|border-image-slice|border-image-repeat|border-image-outset|border-image|border-color|border-collapse|border-clip-top|border-clip-right|border-clip-left|border-clip-bottom|border-clip|border-bottom-width|border-bottom-style|border-bottom-right-radius|border-bottom-left-radius|border-bottom-color|border-bottom|border-block-start-width|border-block-start-style|border-block-start-color|border-block-start|border-block-end-width|border-block-end-style|border-block-end-color|border-block-end|border|bookmark-target|bookmark-level|bookmark-label|bookmark|block-size|binding|bidi|before|baseline-shift|baseline|balance|background-size|background-repeat|background-position-y|background-position-x|background-position-inline|background-position-block|background-position|background-origin|background-image|background-color|background-clip|background-blend-mode|background-attachment|background|backface-visibility|backdrop-filter|azimuth|attachment|appearance|animation-timing-function|animation-play-state|animation-name|animation-iteration-count|animation-fill-mode|animation-duration|animation-direction|animation-delay|animation|alt|all|alignment-baseline|alignment-adjust|alignment|align-last|align-self|align-items|align-content|align|after|adjust|additive-symbols)</string>
							<key>name</key>
							<string>support.type.property-name.css</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>(:)\s*</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.key-value.css</string>
						</dict>
					</dict>
					<key>end</key>
					<string>\s*(;|(?=\}))</string>
					<key>endCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.terminator.rule.css</string>
						</dict>
					</dict>
					<key>name</key>
					<string>meta.property-value.css</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#property-values</string>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<key>selector</key>
		<dict>
			<key>begin</key>
			<string>\s*(?=[:.*#a-zA-Z])</string>
			<key>end</key>
			<string>(?=[/@{)])</string>
			<key>name</key>
			<string>meta.selector.css</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>\b(a|abbr|address|area|article|aside|audio|b|base|bdi|bdo|blockquote|body|br|button|canvas|caption|cite|code|col|colgroup|data|datalist|dd|del|details|dfn|dialog|div|dl|dt|em|embed|fieldset|figcaption|figure|footer|form|(h[1-6])|head|header|hgroup|hr|html|i|iframe|img|input|ins|kbd|label|legend|li|link|main|map|mark|menu|menuitem|meta|meter|nav|noscript|object|ol|optgroup|option|output|p|param|picture|pre|progress|q|rb|rp|rt|rtc|ruby|s|samp|script|section|select|small|source|span|strong|style|sub|summary|sup|svg|table|tbody|td|template|textarea|tfoot|th|thead|time|title|tr|track|u|ul|var|video|wbr)\b</string>
					<key>name</key>
					<string>entity.name.tag.css</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.entity.css</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(\.)[a-zA-Z0-9_-]+</string>
					<key>name</key>
					<string>entity.other.attribute-name.class.css</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.entity.css</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(#)[a-zA-Z][a-zA-Z0-9_-]*</string>
					<key>name</key>
					<string>entity.other.attribute-name.id.css</string>
				</dict>
				<dict>
					<key>match</key>
					<string>\*</string>
					<key>name</key>
					<string>entity.name.tag.wildcard.css</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.entity.css</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(:+)(after|backdrop|before|first-letter|first-line|placeholder|selection)\b</string>
					<key>name</key>
					<string>entity.other.attribute-name.pseudo-element.css</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.entity.css</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(:)((first|last)-child|(first|last|only)-of-type|empty|root|target|fullscreen|first|left|right|scope)\b</string>
					<key>name</key>
					<string>entity.other.attribute-name.pseudo-class.css</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.entity.css</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(:)(checked|enabled|default|disabled|indeterminate|in-range|invalid|optional|out-of-range|read-only|read-write|required|valid)\b</string>
					<key>name</key>
					<string>entity.other.attribute-name.pseudo-class.ui-state.css</string>
				</dict>
				<dict>
					<key>begin</key>
					<string>((:)not)(\()</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>entity.other.attribute-name.pseudo-class.css</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.entity.css</string>
						</dict>
						<key>3</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.function.css</string>
						</dict>
					</dict>
					<key>end</key>
					<string>\)</string>
					<key>endCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.function.css</string>
						</dict>
					</dict>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#selector</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>entity.other.attribute-name.pseudo-class.css</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.entity.css</string>
						</dict>
						<key>3</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.function.css</string>
						</dict>
						<key>4</key>
						<dict>
							<key>name</key>
							<string>constant.numeric.css</string>
						</dict>
						<key>5</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.function.css</string>
						</dict>
					</dict>
					<key>match</key>
					<string>((:)nth-(?:(?:last-)?child|(?:last-)?of-type))(\()(\-?(?:\d+n?|n)(?:\+\d+)?|even|odd)(\))</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.entity.css</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(:)(active|hover|(any-)?link|visited|focus)\b</string>
					<key>name</key>
					<string>entity.other.attribute-name.pseudo-class.css</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.entity.css</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>entity.other.attribute-name.attribute.css</string>
						</dict>
						<key>3</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.operator.css</string>
						</dict>
						<key>4</key>
						<dict>
							<key>name</key>
							<string>string.unquoted.attribute-value.css</string>
						</dict>
						<key>5</key>
						<dict>
							<key>name</key>
							<string>string.quoted.double.attribute-value.css</string>
						</dict>
						<key>6</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.begin.css</string>
						</dict>
						<key>7</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.end.css</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(?i)(\[)\s*(-?[_a-z\\[[:^ascii:]]][_a-z0-9\-\\[[:^ascii:]]]*)(?:\s*([~|^$*]?=)\s*(?:(-?[_a-z\\[[:^ascii:]]][_a-z0-9\-\\[[:^ascii:]]]*)|((?&gt;(['"])(?:[^\\]|\\.)*?(\6)))))?\s*(\])</string>
					<key>name</key>
					<string>meta.attribute-selector.css</string>
				</dict>
			</array>
		</dict>
		<key>string-double</key>
		<dict>
			<key>begin</key>
			<string>"</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.css</string>
				</dict>
			</dict>
			<key>end</key>
			<string>"</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.css</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.quoted.double.css</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>\\.</string>
					<key>name</key>
					<string>constant.character.escape.css</string>
				</dict>
			</array>
		</dict>
		<key>string-single</key>
		<dict>
			<key>begin</key>
			<string>'</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.css</string>
				</dict>
			</dict>
			<key>end</key>
			<string>'</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.css</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.quoted.single.css</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>\\.</string>
					<key>name</key>
					<string>constant.character.escape.css</string>
				</dict>
			</array>
		</dict>
		<key>variable-name</key>
		<dict>
			<key>match</key>
			<string>\-\-[^:\),]+</string>
			<key>name</key>
			<string>support.type.property-name.variable.css</string>
		</dict>
	</dict>
	<key>scopeName</key>
	<string>source.css</string>
	<key>uuid</key>
	<string>69AA0917-B7BB-11D9-A7E2-000D93C8BE28</string>
</dict>
</plist>

